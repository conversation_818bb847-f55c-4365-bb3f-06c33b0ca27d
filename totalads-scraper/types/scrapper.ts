export interface Table {
	tableData: string[][];
	spanningCells: SpanningCellConfig[];
	tableCaption?: string;
	tableHeaders?: string[];
	tableId?: string;
	tableClass?: string;
	tableSummary?: string;
}

export interface SpanningCellConfig {
	row: number;
	col: number;
	colSpan?: number;
	rowSpan?: number;
}

export interface ContactDetails {
	name?: string;
	email?: string[];
	phone?: string[];
	address?: string;
	socialLinks?: Record<string, string>;
}

export interface TeamMember {
	name: string;
	title?: string;
	bio?: string;
	imageUrl?: string;
	contact?: {
		email?: string;
		phone?: string;
		linkedin?: string;
	};
}

export interface AboutInfo {
	companyDescription?: string;
	foundingInfo?: string;
	missionStatement?: string;
	teamMembers?: TeamMember[];
	companyValues?: string[];
	awards?: string[];
	industries?: string[];
}

export interface ScrapedData {
	title: string | null;
	desc: string | null;
	nestedLinks: string[];
	text: string;
	contactDetails: ContactDetails;
	aboutData: AboutInfo;
}

export interface CompanyInfo {
	companyDescription?: string;
	foundingInfo?: string;
	missionStatement?: string;
	teamMembers?: TeamMember[];
	companyValues?: string[];
	awards?: string[];
	industries?: string[];
}

export interface ScrapeResult {
	title: string | null;
	desc: string | null;
	nestedLinks: string[];
	text: string;
	contactDetails: ContactDetails;
	aboutData: CompanyInfo;
}
