/* eslint-disable no-undef */
import { Page } from "puppeteer";

import {
	CompanyInfo,
	ContactDetails,
	ScrapeResult,
} from "../../types/scrapper";
import { ELEMENTS_TO_REMOVE_FROM_FINAL_HTML } from "../config/constants";
import {
	extractAboutAndTeamInfo,
	findAboutAndTeamPages,
	isAboutDetailsComplete,
	mergeCompanyInfo,
} from "../extractors/about-info";
import { getTitleAndDesc } from "../extractors/basic-info";
import {
	extractContactDetailsFromPage,
	findContactPages,
	isContactDetailsComplete,
	mergeContactDetails,
} from "../extractors/contact-info";
import { getTablesAndReplaceWithPlaceholders } from "../utils/tables";
import {
	convertHTMLToText,
	replaceTablePlaceholdersWithAsciiTables,
} from "../utils/text-processing";
import { normalizeURL } from "../utils/url";
import clusterManager from "./cluster-manager";

export class PageScraper {
	private async removeUnnecessaryElements(page: Page) {
		console.log("Removing unnecessary elements for the page");
		const removedCount = await page.evaluate(
			(ELEMENTS_TO_REMOVE_FROM_FINAL_HTML) => {
				let removedCount = 0;

				document
					.querySelectorAll(
						ELEMENTS_TO_REMOVE_FROM_FINAL_HTML.join(", "),
					)
					.forEach((el) => {
						removedCount++;
						el.remove();
					});
				return removedCount;
			},
			ELEMENTS_TO_REMOVE_FROM_FINAL_HTML,
		);
		console.log(
			`Removed ${removedCount} unnecessary elements from the page`,
		);
	}

	private async getNestedLinks(page: Page) {
		console.log("Getting all nested links in the page");
		const nestedAnchorsHrefs = await page.evaluate(() => {
			const nestedAnchors = document.querySelectorAll("a");
			const nestedAnchorsHrefs: string[] = [];
			nestedAnchors.forEach((nestedAnchor) =>
				nestedAnchorsHrefs.push(nestedAnchor.href),
			);
			return nestedAnchorsHrefs;
		});
		const currentPageURL = new URL(normalizeURL(page.url()));
		const nestedLinks = new Set<string>();
		nestedAnchorsHrefs.forEach((href) => {
			let nestedAnchorHref = href;
			let nestedURL: URL;
			try {
				nestedURL = new URL(normalizeURL(href));
			} catch {
				nestedAnchorHref = `${currentPageURL.origin}${
					nestedAnchorHref.startsWith("/") ? "" : "/"
				}${nestedAnchorHref}`;
				try {
					nestedURL = new URL(normalizeURL(nestedAnchorHref));
				} catch {
					return;
				}
			}
			if (
				nestedURL.origin === currentPageURL.origin &&
				nestedURL.href !== currentPageURL.href
			)
				nestedLinks.add(nestedURL.href);
		});
		console.log(`Nested links: ${Array.from(nestedLinks)}`);
		return nestedLinks;
	}

	private async scrapeTask({
		page,
		url,
	}: {
		page: Page;
		url: string;
	}): Promise<{
		title: string | null;
		desc: null;
		nestedLinks: string[];
		text: string;
		contactDetails: ContactDetails;
		aboutData?: CompanyInfo;
	}> {
		console.log("Attach request listener to abort unwanted requests.");
		await page.setRequestInterception(true);

		page.on("request", (req) => {
			if (
				["image", "media", "font", "texttrack"].includes(
					req.resourceType(),
				)
			)
				req.abort();
			else req.continue();
		});

		console.log(`Navigating to page: ${url}`);
		await page.goto(url, {
			waitUntil: ["load", "domcontentloaded", "networkidle2"],
			timeout: 0,
		});
		console.log(`Navigated to page: ${url} successfully`);

		const { title, desc } = await getTitleAndDesc(page);
		const tables = await getTablesAndReplaceWithPlaceholders(page);
		const nestedLinks = Array.from(await this.getNestedLinks(page));
		await this.removeUnnecessaryElements(page);
		let text = await convertHTMLToText(page);
		text = replaceTablePlaceholdersWithAsciiTables(tables, text);

		// Extract contact details from the current page
		console.log("Extracting contact details from the current page");
		const contactDetails = await extractContactDetailsFromPage(page);
		console.log(`Scraped page: ${url} successfully`);

		return {
			title,
			desc,
			nestedLinks,
			text,
			contactDetails,
		};
	}

	async scrape(url: string): Promise<ScrapeResult> {
		const cluster = clusterManager.getCluster();

		const output = await cluster.execute(
			normalizeURL(url),
			async ({ page, data }) => {
				const basicOutput = await this.scrapeTask({ page, url: data });

				// If contact details are incomplete, try to find more info on other pages
				if (!isContactDetailsComplete(basicOutput.contactDetails)) {
					console.log(
						"Contact details incomplete, looking for contact/about pages",
					);
					const contactLinks = await findContactPages(page, data);

					// Visit each potential contact page until we find complete details
					for (const link of contactLinks) {
						if (
							isContactDetailsComplete(basicOutput.contactDetails)
						)
							break;

						console.log(
							`Navigating to potential contact page: ${link}`,
						);
						try {
							await page.goto(link, {
								waitUntil: [
									"load",
									"domcontentloaded",
									"networkidle2",
								],
								timeout: 30000,
							});

							const pageDetails =
								await extractContactDetailsFromPage(page);
							basicOutput.contactDetails = mergeContactDetails(
								basicOutput.contactDetails,
								pageDetails,
							);
						} catch (error) {
							console.error(
								`Error navigating to ${link}:`,
								error,
							);
						}
					}
				}

				console.log("Extracting about page information");
				const aboutLinks = await findAboutAndTeamPages(
					page,
					data,
					basicOutput.nestedLinks,
				);

				// Visit each potential about page until we find complete details
				for (const link of aboutLinks) {
					if (
						basicOutput.aboutData &&
						isAboutDetailsComplete(basicOutput.aboutData)
					)
						break;

					console.log(`Navigating to potential about page: ${link}`);
					try {
						await page.goto(link, {
							waitUntil: [
								"load",
								"domcontentloaded",
								"networkidle2",
							],
							timeout: 30000,
						});

						const aboutData = await extractAboutAndTeamInfo(page);
						console.log("Merging about data", aboutData);
						basicOutput.aboutData = mergeCompanyInfo(
							basicOutput.aboutData || {},
							aboutData,
						);
					} catch (error) {
						console.error(`Error navigating to ${link}:`, error);
					}
				}

				return {
					...basicOutput,
				};
			},
		);

		return output;
	}
}

export default new PageScraper();
